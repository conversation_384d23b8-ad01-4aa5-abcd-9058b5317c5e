/** 页面布局 copy from AppLayout
  * 预防toolkit更新导致的样式冲突
  * 方便快速修改DB自定义layout样式
*/
.db-app-layout-container {
  background-color: #f7f7f9;
  display: flex;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;

  &.is-private-container {
    top: 50px;
    height: ~'calc(100% - 50px)';
  }

  .app-menu-container {
    position: relative;
    z-index: 99;
    flex-shrink: 0;
    height: 100%;
    display: flex;
    flex-direction: column;
    background-color: #f7f7f9;

    .acud-menu {
      padding: 0 16px;
    }

    .acud-menu.acud-menu-root.acud-menu-inline {
      box-shadow: none;
      background-color: #f7f7f9;

      .acud-menu-sub {
        background-color: #f7f7f9;
      }
    }

    .acud-menu-item-custom {
      .new-button {
        width: 100%;
      }
    }

    .app-menu-collapse {
      margin: 8px;
      padding: 8px;
      width: 16px;
      cursor: pointer;

      &:hover {
        background: #e8e9eb;
        border-radius: 4px;
      }

      &.nav-collapsed {
        margin: 8px 12px;
      }
    }

    .app-menu {
      width: 180px;
      flex: 1;
      z-index: 1;
      overflow-y: auto;
      box-shadow: none !important;
      background-color: transparent;

      .acud-menu-inline-header {
        height: 50px;
        line-height: 22px;
        padding: 16px 0 8px;
        margin: 0;

        .acud-menu-inline-header-item {
          border-bottom: none;
        }
      }

      &.acud-menu-inline-collapsed {
        padding: 0 12px;
        width: 56px;

        .acud-menu-inline-header {
          margin: 0 0 12px 0;
          padding: 0;
          height: 48px;

          .acud-menu-inline-header-item {
            border-bottom: 1px solid #e8e9eb;
            padding-left: 8px;
          }
        }

        .acud-menu-item {
          margin: 8px 0px;
        }

        & > .acud-menu-item-group > .acud-menu-item-group-title {
          margin: 0 12px;
          border-bottom: 1px solid #d4d6d9;
          padding: 0 12px;
        }
      }

      &.with-footer {
        box-shadow: 0px -2px 8px 0px rgba(7, 12, 20, 0.12) !important;
      }

      .acud-menu-inline-header-item {
        color: #84868c !important;
      }

      .acud-menu-item {
        user-select: none;
        margin: 8px 0px;
        padding: 0;
        height: 32px;
        color: #303540;
        font-size: 12px;
        padding-left: 8px !important;

        &-active {
          background: #e8e9eb;
          border-radius: 6px;

          &.app-menu-add-btn {
            background: #e6f0ff;
            border: 1px solid #d4e5ff;
            border-radius: 6px;
            color: #2468f2;
          }
        }

        &-selected {
          background: #e8e9eb;
          border-radius: 6px;
          color: #151b26;
          font-weight: 500;

          &.app-menu-add-btn {
            background: #e6f0ff;
            border: 1px solid #d4e5ff;
            border-radius: 6px;
            color: #2468f2;
          }
        }

        &::before {
          content: none;
        }
      }

      .app-menu-add-btn {
        margin-top: 0px !important;
        background: #e6f0ff;
        border: 1px solid #d4e5ff;
        border-radius: 6px;
        margin-bottom: 12px;
        color: #2468f2;
        padding-left: 7px !important;
      }
    }

    &.hidden-app-menu {
      .app-menu {
        width: 0;
      }

      .app-menu-footer {
        width: 0;
        display: none;
      }
    }

    .app-menu-footer {
      box-sizing: border-box;
      width: 180px;
      padding: 12px;
      box-shadow: 0px 2px 8px 0px rgba(7, 12, 20, 0.12);
    }

    .menu-aside-hide-bar {
      align-items: center;
      background-color: initial;
      box-sizing: border-box;
      cursor: pointer;
      display: flex;
      flex-direction: column;
      height: 50px;
      justify-content: center;
      position: absolute;
      right: -15px;
      top: 50%;
      transform: translateY(-50%);
      width: 16px;
      z-index: 10;

      &::before {
        background: #f7f7f9;
        border-left: 0;
        border-radius: 0 10px 10px 0;
        bottom: 0;
        box-sizing: border-box;
        content: '';
        left: 0;
        position: absolute;
        right: 0;
        top: 0;
        transform: perspective(50px) rotateY(30deg);
        transition: all 0.15s;
        z-index: -1;
      }

      &::after {
        background-color: #a1a6b3;
        content: '';
        height: 8px;
        -webkit-mask-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='6' height='8' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M5.223.518.624 3.584a.5.5 0 0 0 0 .832l4.599 3.066A.5.5 0 0 0 6 7.066V.934a.5.5 0 0 0-.777-.416Z' fill='%23A1A6B3' fill-rule='nonzero'/%3E%3C/svg%3E");
        -webkit-mask-repeat: no-repeat;
        mask-repeat: no-repeat;
        -webkit-mask-size: 100% 100%;
        mask-size: 100% 100%;
        position: absolute;
        top: 50%;
        transform: translate(-50%, -50%) rotate(0deg);
        transition: all 0.15s;
        width: 6px;
      }
    }

    .menu-aside-hide-bar-is-hidden {
      &::after {
        transform: translate(-50%, -50%) rotate(180deg);
      }
    }
  }

  .page-wrapper {
    width: 0;
    flex: 1;
    overflow: auto;
    height: 100%;
    display: flex;
    flex-direction: column;

    .page-header-container {
      background-color: #fff;
      padding: 8px 0 4px;

      .header-content-container {
        padding: 8px 16px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .menu-name {
          color: #151a26;
          font-weight: 500;
          font-size: 16px;
          margin: 0;

          .menu-name-text {
            vertical-align: middle;
          }
        }
      }

      .header-menu {
        height: 40px;
        line-height: 40px;
        box-shadow: none !important;

        .acud-menu-item {
          padding-right: 16px;
          padding-left: 16px;
        }
      }
    }

    .nav-page-content-container {
      background-color: #fff;
      border-radius: 4px;
      padding: 0 16px 16px 16px;
      flex-grow: 1;
    }

    .nav-page-content-without-padding-container {
      padding: 0;
      background-color: transparent;
    }
  }

  .non-nav-page-content-container {
    width: 100%;
    overflow: auto;
  }

  .modifySubtitle {
    margin-left: 8px;
    display: inline-block;
    font-size: 12px;
    transform: scale(0.75);
    transform-origin: left;
    border: 1px solid #ef7101;
    padding: 4px;
    border-radius: 4px;
    color: #ef7101;
    line-height: 12px;
  }

  // workspace 通用wrapper
  .db-workspace-wrapper {
    background: #ffffff;
    border: 1px solid rgba(212, 214, 217, 0.6);
    border-radius: 6px;
    width: 100%;
    padding: 16px;
    margin: 0 8px 8px 0;
    overflow-y: auto;
  }

  .acud-tooltip {
    .acud-tooltip-content {
      .acud-tooltip-arrow {
        .acud-tooltip-arrow-content {
          background-color: #fff;
        }
      }

      .acud-tooltip-inner {
        color: #151b26;
        background-color: #fff;
      }
    }
  }
}

.suspenseContainer {
  width: 100%;
  height: 100%;
  background-color: #fff;
}

.acud-tooltip.app-menu-header-tooltip {
  .acud-tooltip-content {
    .acud-tooltip-arrow {
      .acud-tooltip-arrow-content {
        background-color: #fff;
      }
    }

    .acud-tooltip-inner {
      color: #151b26;
      background-color: #fff;
    }
  }
}

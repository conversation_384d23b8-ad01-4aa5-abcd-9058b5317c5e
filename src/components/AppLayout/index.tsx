/**
 * COPY FROM React-toolkit-AppLayout 0.0.33-beta.1
 * 方便业务侧维护 & 自定义
 */
import {Dropdown, Loading, Menu} from 'acud';
import {OutlinedLink} from 'acud-icon';
import cx from 'classnames';
import React, {memo, ReactElement, ReactNode, Suspense, useCallback, useEffect, useMemo} from 'react';
import {useLocation, useNavigate} from 'react-router-dom';
// TODO：toolkit版本更新后 是否会不适配？目前来看 这几个公共interface & 方法 向前兼容 不适配可能性不大
import {
  toolkitConfig,
  ToolkitConfigOptions,
  AppContextActionType,
  ServiceParam,
  useAppContext
} from '@baidu/bce-react-toolkit';
import {queryIamStsRole} from '@api/auth';
import {MenuItem} from '@type/common';
import {recursiveMenus} from '@utils/utils';
import {useLocalStorageState} from 'ahooks';
import './index.less';
import IconSvg from '@components/IconSvg';
import {OutlinedPlusNew} from 'acud-icon';
import flags from '@/flags';

const {SubMenu, MenuHead, ItemCustom} = Menu;

type AsyncFunction = (...args: any) => Promise<any>;

interface AppLayoutProps {
  /** 渲染的子节点 */
  children: React.ReactNode;
  /** 菜单列表 */
  menus: MenuItem[];
  /** 侧边栏顶部额外内容 */
  sidebarExtra?: ReactNode;
  /** 侧边栏底部额外内容 */
  sidebarFooter?: ReactNode;
  /** 产品开通参数 */
  serviceParams?: ServiceParam[];
  /** 开通页路由 */
  activationPagePath?: string;
  /** 是否启动产品内置的产品开通逻辑，默认为 true */
  enableBuiltInProductActivation?: boolean;
  /** 产品是否需要判断激活, 默认为 true */
  needToVerifyProductActivation?: boolean;
  /** 在页面渲染之前，预执行的异步函数(列表) */
  preInvokeAsyncFunc?: AsyncFunction;
  /** 是否有菜单栏右侧收起按钮 */
  hasCollapseBtn?: boolean;
  /** 是否显示菜单栏顶部标题 */
  hasMenuHead?: boolean;
  /** 菜单栏className */
  menuClassName?: string;
}

export const AppLayout: React.FC<AppLayoutProps> = memo(function AppLayout({
  children,
  menus,
  sidebarExtra,
  sidebarFooter,
  serviceParams,
  activationPagePath,
  preInvokeAsyncFunc,
  enableBuiltInProductActivation = false,
  needToVerifyProductActivation = false,
  hasCollapseBtn = true,
  hasMenuHead = true,
  menuClassName
}: AppLayoutProps) {
  const location = useLocation();
  const {appState, appDispatch} = useAppContext();
  const navigate = useNavigate();

  const [collapsed, setCollapsed] = useLocalStorageState<boolean | undefined>('databuilder-nav-collapsed', {
    defaultValue: false
  });

  const appTitle = toolkitConfig.getConfig('appTitle') as ToolkitConfigOptions['appTitle'];

  const flattenedMenuList = useMemo(() => {
    return recursiveMenus(menus);
  }, [menus]);

  const currentHashPath = useMemo(() => {
    return location.pathname;
  }, [location.pathname]);

  const getMenuItemByKey = useCallback(
    (
      menuList: MenuItem[],
      key = currentHashPath
    ): {
      menuItem: MenuItem;
      headerMenuItem?: MenuItem;
    } | null => {
      for (const item of menuList) {
        if (item.key === key) {
          return {menuItem: item};
        }
        if (item.children?.length) {
          const result = getMenuItemByKey(item.children, key);
          if (result) {
            if (result.menuItem.isHeaderNav) {
              return {
                menuItem: item,
                headerMenuItem: result.menuItem
              };
            }
            return result;
          }
        }
      }
      return null;
    },
    [currentHashPath]
  );

  const [currentMenuName, currentHeaderMenu, selectedKeys, renderSubtitle] = useMemo(() => {
    const {menuItem, headerMenuItem} = getMenuItemByKey(menus) || {};

    return [
      menuItem?.menuName || '',
      headerMenuItem ? (menuItem?.children?.filter((item) => item.isHeaderNav) as MenuItem[]) : [],
      headerMenuItem
        ? [menuItem?.activeMenuKey || (menuItem?.key as string), headerMenuItem.key]
        : [menuItem?.activeMenuKey || menuItem?.key || ''],
      menuItem?.renderSubtitle
    ];
  }, [getMenuItemByKey, menus]);

  const onClickMenuItem = useCallback(
    ({key}: {key: string}) => {
      // 私有化模式下，不进行菜单跳转拦截
      if (!flags.DatabuilderPrivateSwitch && !appState.isActivated) {
        return;
      }

      const {menuItem} = getMenuItemByKey(menus, key) || {};
      const activeItem = flattenedMenuList.find((item) => item.key === location.pathname);

      if (selectedKeys[0] === key) {
        if (activeItem?.isNavMenu) {
          return;
        } else if (!activeItem?.isNavMenu && activeItem?.activeMenuKey) {
          navigate(activeItem?.activeMenuKey);
        }
      }

      let hashPath = menuItem?.key;

      if (menuItem?.hasHeaderMenu) {
        hashPath = menuItem.children?.find((item) => item.isHeaderNav)?.key;
      }

      if (menuItem?.isLink) {
        window.open(menuItem?.key, menuItem?.target ?? '_blank');
        /**解决由react-router接管的url 匹配不到时页面不刷新问题 */
        if (menuItem?.isNeedReload) {
          window.location.reload();
        }
      } else if (hashPath) {
        // 避免 useBlocker 拦截不到 window.location.hash
        // window.location.hash = hashPath;
        navigate(hashPath);
      }
    },
    [appState.isActivated, getMenuItemByKey, menus, selectedKeys]
  );

  const onClickHeaderMenuItem = useCallback(
    ({key}: {key: string}) => {
      if (selectedKeys[1] === key) {
        return;
      }
      // 避免 useBlocker 拦截不到 window.location.hash
      // window.location.hash = key;
      navigate(key);
    },
    [selectedKeys]
  );

  useEffect(() => {
    const preHandler = async (isActivated?: boolean) => {
      if (!preInvokeAsyncFunc) {
        return;
      }

      let ret: any;

      if (Array.isArray(preInvokeAsyncFunc)) {
        for (let i = 0; i < preInvokeAsyncFunc.length; i++) {
          ret = await preInvokeAsyncFunc[i](ret);
        }
        return;
      }

      await preInvokeAsyncFunc(isActivated);
    };

    const fn = async () => {
      if (!needToVerifyProductActivation) {
        await preHandler();
        appDispatch({
          type: AppContextActionType.ACTIVATE_PRODUCT
        });
        appDispatch({
          type: AppContextActionType.COMPLETE_PRE_REQUEST
        });
        return;
      }

      if (enableBuiltInProductActivation) {
        if (serviceParams?.length) {
          const promises = serviceParams.map((item) => {
            return queryIamStsRole({
              roleName: item.roleName
            });
          });

          const results = await Promise.all(promises);
          serviceParams.forEach((item, index) => {
            if (results[index]?.result?.name) {
              item.isActivated = true;
              return;
            }
            item.isActivated = false;
          });

          appDispatch({
            type: AppContextActionType.SET_SERVICE_PARAMS,
            payload: {
              serviceParams
            }
          });
          const isActivated = !serviceParams.some((item) => !item.isActivated);
          await preHandler(isActivated);

          if (!isActivated) {
            if (activationPagePath) {
              navigate(activationPagePath);
            }
          } else {
            appDispatch({
              type: AppContextActionType.ACTIVATE_PRODUCT
            });
          }

          appDispatch({
            type: AppContextActionType.COMPLETE_PRE_REQUEST
          });
        }
        return;
      }

      await preHandler();

      appDispatch({
        type: AppContextActionType.COMPLETE_PRE_REQUEST
      });
    };

    fn();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [serviceParams, preInvokeAsyncFunc, enableBuiltInProductActivation, needToVerifyProductActivation]);

  const renderMenuItem = useCallback(
    (menuItem: MenuItem) => {
      if (menuItem?.children?.length && menuItem.isMenuGroup) {
        return (
          <Menu.ItemGroup title={collapsed ? '' : menuItem.menuName}>
            {menuItem.children.map(renderMenuItem)}
          </Menu.ItemGroup>
        );
      }
      if (menuItem?.children?.length && menuItem.isNavMenu && !menuItem.hasHeaderMenu) {
        const icon = menuItem.icon ? {icon: menuItem.icon} : menuItem.isLink ? {icon: <OutlinedLink />} : {};
        return (
          <SubMenu {...icon} key={menuItem.key} title={menuItem.menuName}>
            {menuItem.children.map(renderMenuItem)}
          </SubMenu>
        );
      }
      const icon = menuItem.icon ? {icon: menuItem.icon} : menuItem.isLink ? {icon: <OutlinedLink />} : {};
      return menuItem.isNavMenu ? (
        <Menu.Item
          {...icon}
          key={menuItem.key}
          onClick={onClickMenuItem}
          data-testid="app-menu-item"
          tooltipProps={{
            overlay: undefined,
            getPopupContainer: () =>
              document.getElementsByClassName('db-app-layout-container')[0] as HTMLElement
          }}
        >
          {menuItem.menuName}
        </Menu.Item>
      ) : null;
    },
    [onClickMenuItem, collapsed]
  );

  const defaultOpenKeys = useMemo(() => {
    const keys: string[] = [];
    const getDefaultOpenKeys = (menuItem: MenuItem) => {
      if (menuItem.isDefaultOpened) {
        keys.push(menuItem.key);
      }
      menuItem?.children?.length &&
        menuItem.children.forEach((item) => {
          if (item.key === selectedKeys[0]) {
            keys.push(menuItem.key);
          }
          getDefaultOpenKeys(item);
        });
    };

    menus.forEach(getDefaultOpenKeys);

    return keys;
  }, [menus, selectedKeys]);

  const renderMenu = useCallback(() => {
    return (
      <div
        className={cx('app-menu-container', menuClassName, {
          'hidden-app-menu': appState.isMenuCollapsed
        })}
      >
        <Menu
          className={cx('app-menu', {
            ['with-footer']: sidebarFooter
          })}
          mode="inline"
          selectedKeys={selectedKeys}
          defaultOpenKeys={defaultOpenKeys}
          data-testid="app-menu-root"
          inlineCollapsed={collapsed}
        >
          {hasMenuHead && !flags.DatabuilderPrivateSwitch && (
            <MenuHead
              data-testid="app-menu-header"
              icon={collapsed ? <IconSvg type="nav-header" size={16} color="#303540" /> : ''}
              popupClassName="app-menu-header-tooltip"
            >
              {appTitle}
            </MenuHead>
          )}
          {sidebarExtra && (
            <Dropdown overlay={sidebarExtra as ReactElement} placement="rightTop">
              <Menu.Item
                className="app-menu-add-btn"
                icon={<IconSvg type="add" size={16} color="#2468F2" />}
                tooltipProps={{visible: false, overlay: ''}}
              >
                新建
              </Menu.Item>
            </Dropdown>
          )}
          {menus.map(renderMenuItem)}
        </Menu>
        {sidebarFooter && <div className={'app-menu-footer'}>{sidebarFooter}</div>}
        {hasCollapseBtn && (
          <IconSvg
            onClick={() => setCollapsed((pre) => !pre)}
            type="nav-collapse"
            size={16}
            color="#5C5F66"
            className={cx('app-menu-collapse', {
              'nav-collapsed': collapsed
            })}
          />
        )}
      </div>
    );
  }, [
    appState.isMenuCollapsed,
    sidebarFooter,
    selectedKeys,
    defaultOpenKeys,
    collapsed,
    hasMenuHead,
    appTitle,
    sidebarExtra,
    menus,
    renderMenuItem,
    hasCollapseBtn,
    setCollapsed
  ]);

  const Header = useCallback(() => {
    const HeaderContent = (
      <div className={'header-content-container'}>
        <div className={'menu-name'}>
          {currentMenuName}
          {renderSubtitle && renderSubtitle({})}
        </div>
      </div>
    );

    return HeaderContent;
  }, [renderSubtitle, currentMenuName]);

  const HeaderMenu = useCallback(() => {
    const HeaderMenuContent = (
      <div className={'header-menu-container'}>
        <Menu
          className={'header-menu'}
          mode="horizontal"
          selectedKeys={selectedKeys.slice(-1)}
          data-testid="app-menu-root"
        >
          {currentHeaderMenu.map((menuItem) => {
            const icon = menuItem.icon ? {icon: menuItem.icon} : {};
            return (
              <Menu.Item
                {...icon}
                key={menuItem.key}
                onClick={onClickHeaderMenuItem}
                data-testid="app-menu-item"
              >
                {menuItem.menuName}
              </Menu.Item>
            );
          })}
        </Menu>
      </div>
    );

    return HeaderMenuContent;
  }, [currentHeaderMenu, onClickHeaderMenuItem, selectedKeys]);

  const renderHeader = useCallback(() => {
    return (
      <div className={cx(['page-header-container', 'page-header'])}>
        <Header />
        {!!currentHeaderMenu.length && <HeaderMenu />}
      </div>
    );
  }, [Header, HeaderMenu, currentHeaderMenu.length]);

  const routes = useMemo(() => {
    const menuList = recursiveMenus(menus);
    return {
      navRoutes: menuList.filter((item) => item.Component && (item.isNavMenu || item.activeMenuKey)),
      nonNavRoutes: menuList.filter((item) => item.Component && !item.isNavMenu && !item.activeMenuKey)
    };
  }, [menus]);

  const isNavPage = useMemo(() => {
    return routes.navRoutes.findIndex((item) => item.key === currentHashPath) > -1;
  }, [currentHashPath, routes.navRoutes]);

  const currentMenu: MenuItem | undefined = useMemo(() => {
    const index = flattenedMenuList.findIndex((item) => item.key === currentHashPath);
    if (index > -1) {
      return flattenedMenuList[index];
    }
  }, [currentHashPath, flattenedMenuList]);

  const renderSuspense = () => {
    return <div className={'suspense-container'}></div>;
  };

  const renderNavPage = useCallback(() => {
    return (
      <>
        {renderMenu()}
        {currentMenu?.isPageLayoutCustomized ? (
          <Suspense fallback={renderSuspense()}>{children}</Suspense>
        ) : (
          <div className={'page-wrapper'}>
            {renderHeader()}
            <Suspense fallback={renderSuspense()}>
              <div
                className={cx({
                  ['nav-page-content-container']: true,
                  ['nav-page-content-without-padding-container']:
                    (!flags.DatabuilderPrivateSwitch && !appState.isActivated) ||
                    currentMenu?.isPageWrapperNotRequired,
                  ['page-content']: true
                })}
              >
                {children}
              </div>
            </Suspense>
          </div>
        )}
      </>
    );
  }, [
    appState.isActivated,
    children,
    currentMenu?.isPageLayoutCustomized,
    currentMenu?.isPageWrapperNotRequired,
    renderHeader,
    renderMenu
  ]);

  const renderNonNavPage = useCallback(() => {
    return (
      <Suspense fallback={<Loading size="small"></Loading>}>
        <div className={'non-nav-page-content-container'}>{children}</div>
      </Suspense>
    );
  }, [children]);

  return (
    <div
      className={cx('db-app-layout-container', {
        'is-private-container': flags.DatabuilderPrivateSwitch
      })}
    >
      {!appState.isPreRequestCompleted ? (
        <Loading loading={true} size="small"></Loading>
      ) : isNavPage ? (
        renderNavPage()
      ) : (
        renderNonNavPage()
      )}
    </div>
  );
});

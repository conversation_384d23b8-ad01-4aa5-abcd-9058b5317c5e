// 退出登录
export const accountLogout = () => {
  const a = document.createElement('a');

  // 添加重定向地址，优先读取配置内容
  // const defaultRedirect = `/logout?redirect=${window.location.origin}/login`;
  // a.href = window?.PRIVATE_STATIC?.mainPageConfig?.headerInfo?.logoutUrl || defaultRedirect;
  // a.href = defaultRedirect;
  // FIXME: 暂时处理固定退出登录地址， 后面添加 private.config.js 文件
  a.href =
    '/logout?redirect=http://idaas.agilecloud-yq.com:80/login?redirect=http://bigdata.agilecloud-yq.com:80';
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
};

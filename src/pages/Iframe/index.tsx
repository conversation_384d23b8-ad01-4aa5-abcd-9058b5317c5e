/**
 * iframe 内嵌 EDAP 页面
 */

import React, {FC, useEffect, useLayoutEffect} from 'react';
import './index.less';
import {hideIframe, showIframe} from '@components/IframePreloader';

const IframeEdapPageView: FC = () => {
  // FIXME： 经过调试，始终达不到预期效果，先用这个方式实现，后续结构化列表页面重新开发
  useLayoutEffect(() => {
    showIframe('edap-iframe', document.getElementById('iframe-div-box') as Element);
    return () => {
      hideIframe('edap-iframe');
    };
  }, []);

  return (
    <div className="iframe-page-box">
      <div
        id="iframe-div-box"
        style={{
          width: '100%',
          height: '100%',
          transition: 'opacity 0.3s ease-in-out'
        }}
      />
    </div>
  );
};

export default React.memo(IframeEdapPageView);
